package cn.edu.xmut.soms.admin.module.asset.server.constant;

import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务器类型
 *
 * <AUTHOR>
 * @Date 2021-10-25 20:26:54
 */
@AllArgsConstructor
@Getter
public enum ServerTypeEnum implements BaseEnum {

    /**
     * 0 物理机
     */
    PhysicalMachine(0, "物理机"),

    /**
     * 1 虚拟机
     */
    VirtualMachine(1, "虚拟机"),

    ;

    private final Integer value;

    private final String desc;
}
