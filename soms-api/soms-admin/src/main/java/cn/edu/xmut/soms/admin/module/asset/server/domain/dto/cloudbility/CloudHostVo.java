package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.cloudbility;

import lombok.Data;

import java.util.List;

/**
 * 行云管家接口返回 - 虚拟机信息
 *
 * <AUTHOR>
 * @Date 2023/11/19 20:04
 */
@Data
public class CloudHostVo {

    /**
     * Agent的状态:STOPPED,READY
     */
    private String agentStatus;

    /**
     * 云账户ID
     */
    private Long cloudId;

    /**
     * 云账号名称
     */
    private String cloudName;

    /**
     * 数据盘
     */
    private List<DataDiskVo> dataDisks;

    /**
     * 主机描述
     */
    private String description;

    /**
     * 实例过期时间
     */
    private String expiredTime;

    /**
     * 主机所在组ID
     */
    private Integer groupId;

    /**
     * 主机ID
     */
    private Long hostId;

    /**
     * 主机名称
     */
    private String hostName;

    /**
     * 导入时间
     */
    private String importTime;

    /**
     * 内网IP
     */
    private String innerIp;

    /**
     * 是否安装了Agent
     */
    private Boolean installedAgent;

    /**
     * 实例ID
     */
    private String instanceId;

    /**
     * 主机名称
     */
    private String instanceName;

    /**
     * 任务ID
     */
    private Long jobId;

    /**
     * MAC地址
     */
    private String macAddr;

    /**
     * 内存大小
     */
    private Integer memory;

    /**
     * 操作系统
     */
    private String operatingSystem;

    /**
     * 系统平台
     */
    private String platform;

    /**
     * CPU核数
     */
    private Integer processorCores;

    /**
     * 云厂商
     */
    private String provider;

    /**
     * 主机能够访问的Proxy所在主机的ID
     */
    private Long proxyHostId;

    /**
     * 主机能够访问的Proxy内网IP
     */
    private String proxyInnerIp;

    /**
     * 主机能够访问的Proxy所在主机的是否已安装Agent
     */
    private Boolean proxyInstalledAgent;

    /**
     * 主机能够访问的Proxy所在主机的实例名称
     */
    private String proxyInstanceName;

    /**
     * 主机能够访问的Proxy公网IP
     */
    private String proxyPublicIp;

    /**
     * 公网IP
     */
    private String publicIp;

    /**
     * 区域ID
     */
    private String regionId;

    /**
     * 云账户来源，门户：Portal|管理控制台：Console = ['Console', 'Portal']
     */
    private String scope;

    /**
     * 主机状态
     */
    private String status;

    /**
     * 系统盘信息
     */
    private SystemDiskVo systemDisk;

    /**
     * 所属团队ID
     */
    private Long teamId;

    /**
     * 可用区ID
     */
    private String zoneId;
}
