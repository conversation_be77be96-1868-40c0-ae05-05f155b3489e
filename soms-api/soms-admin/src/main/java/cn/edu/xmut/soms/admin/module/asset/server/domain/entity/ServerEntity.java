package cn.edu.xmut.soms.admin.module.asset.server.domain.entity;

import cn.edu.xmut.soms.admin.module.asset.server.constant.ServerTypeEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * 服务器信息表 实体类
 *
 * <AUTHOR>
 * @Date 2024-03-18 11:16:39
 * @Copyright xmut
 */

@Data
@TableName("t_server")
public class ServerEntity {

    /**
     * 服务器ID
     */
    @TableId
    private Long serverId;

    /**
     * 服务器类型 0-物理机，1-虚拟机
     *
     * @see ServerTypeEnum
     */
    private Integer serverType;

    /**
     * 主机ID
     */
    private Long hostId;

    /**
     * 主机名称
     */
    private String hostName;

    /**
     * 实例ID
     */
    private String instanceId;

    /**
     * 实例名称
     */
    private String instanceName;

    /**
     * 操作系统
     */
    private String operatingSystem;

    /**
     * 系统平台
     */
    private String platform;

    /**
     * 公网IP
     */
    private String publicIp;

    /**
     * 内网IP
     */
    private String innerIp;

    /**
     * 主机状态
     */
    private String status;

    /**
     * CPU核数
     */
    private Integer processorCores;

    /**
     * 内存大小
     */
    private Integer memory;

    /**
     * 主机描述
     */
    private String description;

    /**
     * 系统盘大小
     */
    private Integer systemDisk;

    /**
     * 数据盘大小
     */
    private Integer dataDisk;

    /**
     * 云平台名称
     */
    private String cloudName;

    /**
     * 工作空间名称
     */
    private String workspaceName;

    /**
     * 是否删除
     */
    private Boolean deleteFlag;

    /**
     * 添加日期
     */
    private LocalDate addDate;

    /**
     * 更新日期
     */
    private LocalDate updateDate;

    /**
     * 删除日期
     */
    private LocalDate deleteDate;

    /**
     * 是否连接
     */
    private Boolean connectedFlag;

    /**
     * 是否互联网访问
     */
    private Boolean internetAccessFlag;

    /**
     * 使用状态
     */
    private Integer usageStatus;

    /**
     * 用途
     */
    private String serverUsage;

    /**
     * 管理员ID
     */
    private Long managerId;

    /**
     * 云账户ID
     */
    private Long cloudId;

    /**
     * IP列表
     */
    private String ipArray;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 手动录入IP
     */
    private String manualIp;

    /**
     * NAT IP
     */
    private String natIp;

}
