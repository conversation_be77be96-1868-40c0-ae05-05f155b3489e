package cn.edu.xmut.soms.admin.module.asset.server.manager;

import cn.edu.xmut.soms.admin.module.asset.server.domain.dto.cloudbility.*;
import cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerEntity;
import cn.edu.xmut.soms.base.common.util.SmartBeanUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行云管家接口
 *
 * <AUTHOR>
 * @Date 2023/11/19 21:36
 */
@Service
@Slf4j
public class CloudbilityManager {

    private static final String BASE_URL = "https://172.28.240.25/api/openapi";

    private static final String ACCESS_KEY_ID = "N1rGMdQVTGt4nDzE";

    private static final String ACCESS_KEY_SECRET = "w4tzjdSsZyRJXb3DdtIIxEw637AvfV";

    private static final String TEAM_ID = "182826779545600";

    public Map<Long, String> queryCloud() {
        String url = BASE_URL + "/cloud/byTeam/" + TEAM_ID;
        String httpResult = HttpRequest.get(url).header("Authorization", getAccessToken()).execute().body();
        ListCloudsResult listCloudsResult = JSONUtil.toBean(httpResult, ListCloudsResult.class);
        Map<Long, String> cloudMap = new HashMap<>();
        for (CloudVo cloud : listCloudsResult.getClouds()) {
            cloudMap.put(cloud.getId(), cloud.getName());
        }
        return cloudMap;
    }

    public List<ServerEntity> queryCloudHost() {
        String url = BASE_URL + "/host/byTeam/" + TEAM_ID + "?page=1&size=99999";
        String httpResult = HttpRequest.get(url).header("Authorization", getAccessToken()).execute().body();
        CloudHostResult cloudHostResult = JSONUtil.toBean(httpResult, CloudHostResult.class);
        Map<String, CloudHostVo> cloudHostMap = new HashMap<>();
        for (CloudHostVo cloudHostVo : cloudHostResult.getHosts()) {
            cloudHostMap.put(cloudHostVo.getInstanceId(), cloudHostVo);
        }
        List<ServerEntity> cloudServerList = SmartBeanUtil.copyList(cloudHostResult.getHosts(), ServerEntity.class);
        for (ServerEntity server : cloudServerList) {
            CloudHostVo cloudHostVo = cloudHostMap.get(server.getInstanceId());
            server.setServerId(server.getHostId());
            if (ObjectUtil.isNotEmpty(cloudHostVo.getSystemDisk())) {
                server.setSystemDisk(cloudHostVo.getSystemDisk().getSize());
            }
            if (ObjectUtil.isNotEmpty(cloudHostVo.getDataDisks())) {
                int size = cloudHostVo.getDataDisks().stream()
                        .mapToInt(DataDiskVo::getSize)
                        .sum();
                server.setDataDisk(size);
            }
        }
        return cloudServerList;
    }

    private String getAccessToken() {
        String url = BASE_URL + "/oauth?accessKeyId=" + ACCESS_KEY_ID + "&accessKeySecret=" + ACCESS_KEY_SECRET + "&expireSeconds=3600";

        String result = HttpUtil.get(url);
        Dict re = JSONUtil.toBean(result, Dict.class);
        return re.getStr("token");
    }
}
