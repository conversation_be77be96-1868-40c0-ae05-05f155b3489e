package cn.edu.xmut.soms.admin.module.asset.server.manager;

import cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware.VcenterVMSummary;
import cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware.VcenterVmGuestNetworkingInterfacesInfo;
import cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware.VcenterVmGuestNetworkingInterfacesIpAddressInfo;
import cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware.VcenterVmPowerInfo;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/11/19 22:25
 */
@Service
@Slf4j
public class VmwareManager {

    private static final Map<String, String> vmwareUrl = new HashMap<>();

    private static final String USERNAME = "<EMAIL>";

    private static final String PASSWORD = "Vcsa@clh9527!";

    public VmwareManager() {
        vmwareUrl.put("VMWare-100", "https://**************");
        vmwareUrl.put("VMWare-70", "https://172.28.240.70");
        vmwareUrl.put("VMWare-50", "https://172.28.240.50");
        vmwareUrl.put("VMWare-20", "https://172.28.240.20");
        vmwareUrl.put("财务虚拟化平台", "https://172.28.240.80");
//        vmwareUrl.put("V6", "https://vsanvc6.xmut.edu.cn");
    }

    public String getPowerState(String cloud, String sessionId, String vm) {
        String baseUrl = vmwareUrl.get(cloud);
        if (Strings.isEmpty(baseUrl)) {
            return null;
        }
        String powerInfoUrl = baseUrl + "/api/vcenter/vm/" + vm + "/power";
        HttpResponse httpResult = HttpRequest.get(powerInfoUrl).header("vmware-api-session-id", sessionId).execute();
        if (httpResult.getStatus() != 200) {
            return null;
        }
        VcenterVmPowerInfo powerInfo = JSONUtil.toBean(httpResult.body(), VcenterVmPowerInfo.class);
        return powerInfo.getState();
    }

    public String queryIpArray(String cloud, String sessionId, String vm) {
        String baseUrl = vmwareUrl.get(cloud);
        if (Strings.isEmpty(baseUrl)) {
            return null;
        }
        String vmNetworkingInfoUrl = baseUrl + "/api/vcenter/vm/" + vm + "/guest/networking/interfaces";
        HttpResponse httpResult = HttpRequest.get(vmNetworkingInfoUrl).header("vmware-api-session-id", sessionId).execute();
        if (httpResult.getStatus() != 200) {
            return null;
        }
        List<VcenterVmGuestNetworkingInterfacesInfo> networkList = JSONUtil.toList(httpResult.body(), VcenterVmGuestNetworkingInterfacesInfo.class);
        List<String> ipList = new ArrayList<>();
        for (VcenterVmGuestNetworkingInterfacesInfo network : networkList) {
            if (ObjectUtil.isEmpty(network) || ObjectUtil.isEmpty(network.getIp())) {
                continue;
            }
            for (VcenterVmGuestNetworkingInterfacesIpAddressInfo ipAddressInfo : network.getIp().getIp_addresses()) {
                ipList.add(ipAddressInfo.getIp_address());
            }
        }
        if (ipList.isEmpty()) {
            return null;
        }
        return JSONUtil.toJsonStr(ipList);
    }

    public List<VcenterVMSummary> queryAllVM(String cloud, String sessionId) {
        String baseUrl = vmwareUrl.get(cloud);
        if (Strings.isEmpty(baseUrl)) {
            return null;
        }
        String vmUrl = baseUrl + "/api/vcenter/vm";
        HttpResponse httpResult = HttpRequest.get(vmUrl).header("vmware-api-session-id", sessionId).execute();
        if (httpResult.getStatus() != 200) {
            return null;
        }
        return JSONUtil.toList(httpResult.body(), VcenterVMSummary.class);

    }

    @Cacheable("VMWARE_SESSION_ID")
    public String getSessionId(String cloud) {
        String baseUrl = vmwareUrl.get(cloud);
        if (Strings.isEmpty(baseUrl)) {
            return null;
        }
        String sessionUrl = baseUrl + "/rest/com/vmware/cis/session";
        String result = HttpRequest.post(sessionUrl).header("Authorization", "Basic " + Base64.encode(USERNAME + ":" + PASSWORD)).execute().body();
        Dict resultDict = JSONUtil.toBean(result, Dict.class);
        return resultDict.getStr("value");
    }

    public static void main(String[] args) {
        vmwareUrl.put("VMWare-100", "https://**************");
        vmwareUrl.put("VMWare-70", "https://172.28.240.70");
        vmwareUrl.put("VMWare-50", "https://172.28.240.50");
        vmwareUrl.put("VMWare-20", "https://172.28.240.20");
        vmwareUrl.put("财务虚拟化平台", "https://172.28.240.80");
        // 获取网卡
//        String baseUrl = vmwareUrl.get("VMWare-100");
//        String sessionUrl = baseUrl + "/rest/com/vmware/cis/session";
//        String result = HttpRequest.post(sessionUrl).header("Authorization", "Basic " + Base64.encode("<EMAIL>:Vcsa@clh9527!")).execute().body();
//        System.out.println(result);
//        Dict re = JSONUtil.toBean(result, Dict.class);
//        String vmNetworkingInfoUrl = baseUrl + "/api/vcenter/vm/vm-112/guest/networking/interfaces";
//        String result1 = HttpRequest.get(vmNetworkingInfoUrl).header("vmware-api-session-id", re.getStr("value")).execute().body();
//        System.out.println(JSONUtil.parseArray(result1));

        // 获取电源信息
//        String baseUrl = vmwareUrl.get("VMWare-50");
//        String powerInfoUrl = baseUrl + "/api/vcenter/vm/vm-1437/power";
//        String sessionUrl = baseUrl + "/rest/com/vmware/cis/session";
//        String result = HttpRequest.post(sessionUrl).header("Authorization", "Basic " + Base64.encode("<EMAIL>:Vcsa@clh9527!")).execute().body();
//        Dict re = JSONUtil.toBean(result, Dict.class);
//        HttpResponse httpResult = HttpRequest.get(powerInfoUrl).header("vmware-api-session-id", re.getStr("value")).execute();
//        System.out.println(httpResult.body());

        // 获取某个集群所有虚拟机
        String baseUrl = vmwareUrl.get("VMWare-70");
        String sessionUrl = baseUrl + "/rest/com/vmware/cis/session";
        String result = HttpRequest.post(sessionUrl).header("Authorization", "Basic " + Base64.encode("<EMAIL>:Vcsa@clh9527!")).execute().body();
        Dict re = JSONUtil.toBean(result, Dict.class);
        String vmUrl = baseUrl + "/api/vcenter/vm";
        HttpResponse httpResult = HttpRequest.get(vmUrl).header("vmware-api-session-id", re.getStr("value")).execute();
        System.out.println(httpResult.body());
    }

}
